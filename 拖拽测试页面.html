<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>可拖拽滚动插件测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      line-height: 1.6;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 200vh;
    }
    
    .test-container {
      background: white;
      border-radius: 10px;
      padding: 30px;
      margin: 20px 0;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .feature-demo {
      background: #f8f9fa;
      border: 2px dashed #dee2e6;
      border-radius: 8px;
      padding: 20px;
      margin: 15px 0;
      text-align: center;
    }
    
    .highlight {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
    }
    
    .content-block {
      background: #e3f2fd;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      min-height: 200px;
    }
    
    .dynamic-content {
      background: #e8f5e8;
      border-left: 4px solid #4caf50;
    }
    
    #load-more {
      display: block;
      margin: 20px auto;
      padding: 12px 24px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      transition: background 0.3s;
    }
    
    #load-more:hover {
      background: #0056b3;
    }
    
    .instructions {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
    }
    
    .step {
      margin: 10px 0;
      padding: 10px;
      background: white;
      border-radius: 5px;
      border-left: 4px solid #007bff;
    }
  </style>
  
  <!-- 包含插件样式和脚本 -->
  <link rel="stylesheet" href="样式.css">
</head>
<body>
  <div class="test-container">
    <h1>🎯 可拖拽滚动插件测试页面</h1>
    
    <div class="instructions">
      <h2>📋 测试说明</h2>
      <div class="step">
        <strong>1. 拖拽测试:</strong> 按住右下角的蓝色按钮，拖拽到屏幕任意位置
      </div>
      <div class="step">
        <strong>2. 点击测试:</strong> 轻点按钮开始/停止自动滚动
      </div>
      <div class="step">
        <strong>3. 方法切换:</strong> 右键点击按钮切换滚动方法
      </div>
      <div class="step">
        <strong>4. 位置记忆:</strong> 刷新页面后按钮位置应该保持不变
      </div>
      <div class="step">
        <strong>5. 动态内容:</strong> 点击"加载更多"测试动态滚动
      </div>
    </div>
    
    <div class="feature-demo">
      <h3>🖱️ 拖拽功能演示区域</h3>
      <p>将滚动按钮拖拽到这个区域试试！</p>
      <p>按钮应该能够平滑移动，并且在释放后保持新位置。</p>
    </div>
  </div>
  
  <div class="test-container">
    <h2>📄 内容区域 1</h2>
    <p>这是第一个内容区域。测试拖拽功能时，可以将按钮移动到不同位置。</p>
    <div class="highlight">
      <strong>提示:</strong> 拖拽时按钮会变大并显示抓取光标，释放后恢复正常大小。
    </div>
  </div>
  
  <div class="content-block">
    <h2>📄 内容区域 2</h2>
    <p>这个区域用于测试滚动功能。页面足够长，可以测试自动滚动到底部的效果。</p>
    <p>当前时间: <span id="time1"></span></p>
  </div>
  
  <div class="content-block">
    <h2>📄 内容区域 3</h2>
    <p>继续增加页面高度，确保有足够的滚动空间。</p>
    <p>当前时间: <span id="time2"></span></p>
  </div>
  
  <div class="content-block">
    <h2>📄 内容区域 4</h2>
    <p>测试不同滚动方法的兼容性。</p>
    <p>当前时间: <span id="time3"></span></p>
  </div>
  
  <div id="dynamic-container">
    <!-- 动态内容将在这里加载 -->
  </div>
  
  <button id="load-more">🔄 加载更多内容</button>
  
  <div class="test-container" style="background: #ffebee;">
    <h2>🎯 页面底部标记</h2>
    <p>如果滚动功能正常，点击滚动按钮后应该能自动滚动到这里。</p>
    <p>最后更新时间: <span id="bottom-time"></span></p>
    
    <div class="feature-demo">
      <h3>✅ 测试完成检查项</h3>
      <ul style="text-align: left; display: inline-block;">
        <li>□ 按钮可以拖拽移动</li>
        <li>□ 拖拽后位置保持不变</li>
        <li>□ 点击可以开始/停止滚动</li>
        <li>□ 右键可以切换滚动方法</li>
        <li>□ 刷新页面后位置记忆有效</li>
        <li>□ 动态内容滚动正常</li>
      </ul>
    </div>
  </div>
  
  <!-- 包含插件脚本 -->
  <script src="内容脚本.js"></script>
  
  <script>
    // 更新时间显示
    function updateTimes() {
      const now = new Date().toLocaleTimeString();
      for (let i = 1; i <= 3; i++) {
        const element = document.getElementById(`time${i}`);
        if (element) element.textContent = now;
      }
      const bottomElement = document.getElementById('bottom-time');
      if (bottomElement) bottomElement.textContent = now;
    }
    
    // 每秒更新时间
    setInterval(updateTimes, 1000);
    updateTimes();
    
    let contentCounter = 5;
    
    document.getElementById('load-more').addEventListener('click', function() {
      const container = document.getElementById('dynamic-container');
      
      // 创建新的内容块
      const newBlock = document.createElement('div');
      newBlock.className = 'content-block dynamic-content';
      newBlock.innerHTML = `
        <h2>🆕 动态内容块 ${contentCounter}</h2>
        <p>这是动态加载的内容块 ${contentCounter}。测试滚动插件是否能检测到新内容。</p>
        <p>加载时间: ${new Date().toLocaleTimeString()}</p>
        <p>拖拽按钮到这个新区域试试看！</p>
      `;
      
      container.appendChild(newBlock);
      contentCounter++;
      
      console.log(`新内容块 ${contentCounter - 1} 已加载`);
    });
    
    // 页面加载提示
    console.log('拖拽测试页面已加载，可以开始测试拖拽功能！');
  </script>
</body>
</html>
