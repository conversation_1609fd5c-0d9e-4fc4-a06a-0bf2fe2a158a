// 自动滚动插件主脚本 - 多种滚动方法版本
(function() {
    'use strict';

    let scrollButton = null;
    let isScrolling = false;
    let scrollInterval = null;
    let scrollMethodIndex = 0;
    let lastScrollHeight = 0;
    let scrollAttempts = 0;

    // 多种滚动方法
    const scrollMethods = [
        // 方法1: 标准window.scrollTo
        function(height) {
            console.log('使用方法1: window.scrollTo smooth');
            window.scrollTo({
                top: height,
                behavior: 'smooth'
            });
        },

        // 方法2: 直接设置scrollTop
        function(height) {
            console.log('使用方法2: 直接设置scrollTop');
            document.documentElement.scrollTop = height;
            document.body.scrollTop = height;
        },

        // 方法3: 使用scrollIntoView
        function(height) {
            console.log('使用方法3: scrollIntoView');
            const lastElement = document.body.lastElementChild;
            if (lastElement) {
                lastElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
            }
        },

        // 方法4: 查找可滚动容器
        function(height) {
            console.log('使用方法4: 查找可滚动容器');
            const scrollableElements = findScrollableElements();
            scrollableElements.forEach(element => {
                element.scrollTop = element.scrollHeight;
            });
        },

        // 方法5: 模拟键盘End键
        function(height) {
            console.log('使用方法5: 模拟End键');
            document.body.focus();
            const event = new KeyboardEvent('keydown', {
                key: 'End',
                code: 'End',
                keyCode: 35,
                which: 35,
                ctrlKey: true
            });
            document.dispatchEvent(event);
        },

        // 方法6: 逐步滚动
        function(height) {
            console.log('使用方法6: 逐步滚动');
            const currentScroll = window.pageYOffset || document.documentElement.scrollTop;
            const step = Math.max(200, (height - currentScroll) / 10);

            function stepScroll() {
                const current = window.pageYOffset || document.documentElement.scrollTop;
                if (current < height - 50) {
                    window.scrollBy(0, step);
                    setTimeout(stepScroll, 100);
                }
            }
            stepScroll();
        }
    ];

    // 查找页面中所有可滚动的元素
    function findScrollableElements() {
        const elements = [];
        const allElements = document.querySelectorAll('*');

        allElements.forEach(element => {
            const style = window.getComputedStyle(element);
            const hasScroll = element.scrollHeight > element.clientHeight;
            const canScroll = style.overflowY === 'scroll' ||
                            style.overflowY === 'auto' ||
                            style.overflow === 'scroll' ||
                            style.overflow === 'auto';

            if (hasScroll && canScroll) {
                elements.push(element);
            }
        });

        // 添加常见的滚动容器
        const commonSelectors = [
            'body', 'html', '.scroll-container', '.content',
            '#content', '.main', '#main', '.page', '#page',
            '[data-scroll]', '.infinite-scroll', '.feed'
        ];

        commonSelectors.forEach(selector => {
            try {
                const element = document.querySelector(selector);
                if (element && element.scrollHeight > element.clientHeight) {
                    elements.push(element);
                }
            } catch (e) {
                // 忽略无效选择器
            }
        });

        return [...new Set(elements)]; // 去重
    }

    // 拖拽相关变量
    let isDragging = false;
    let dragStartX = 0;
    let dragStartY = 0;
    let buttonStartX = 0;
    let buttonStartY = 0;
    let clickStartTime = 0;
    let hasMoved = false;

    // 创建悬浮按钮
    function createScrollButton() {
        if (scrollButton) return;

        scrollButton = document.createElement('button');
        scrollButton.className = 'auto-scroll-button';
        scrollButton.innerHTML = '↓';
        scrollButton.title = '点击滚动到底部 (可拖拽移动)';

        // 从本地存储恢复位置
        restoreButtonPosition();

        // 添加鼠标事件
        scrollButton.addEventListener('mousedown', handleMouseDown);
        scrollButton.addEventListener('contextmenu', handleRightClick);

        // 添加触摸事件（移动端支持）
        scrollButton.addEventListener('touchstart', handleTouchStart, { passive: false });

        // 添加到页面
        document.body.appendChild(scrollButton);

        console.log('可拖拽滚动按钮已创建');
    }

    // 恢复按钮位置
    function restoreButtonPosition() {
        try {
            const savedPosition = localStorage.getItem('scrollButtonPosition');
            if (savedPosition) {
                const { right, bottom } = JSON.parse(savedPosition);
                scrollButton.style.right = right + 'px';
                scrollButton.style.bottom = bottom + 'px';
                console.log('恢复按钮位置:', { right, bottom });
            }
        } catch (e) {
            console.log('无法恢复按钮位置，使用默认位置');
        }
    }

    // 保存按钮位置
    function saveButtonPosition() {
        try {
            const rect = scrollButton.getBoundingClientRect();
            const position = {
                right: window.innerWidth - rect.right,
                bottom: window.innerHeight - rect.bottom
            };
            localStorage.setItem('scrollButtonPosition', JSON.stringify(position));
            console.log('保存按钮位置:', position);
        } catch (e) {
            console.log('无法保存按钮位置');
        }
    }

    // 处理鼠标按下
    function handleMouseDown(e) {
        if (e.button !== 0) return; // 只处理左键

        e.preventDefault();
        e.stopPropagation();

        isDragging = true;
        hasMoved = false;
        clickStartTime = Date.now();

        dragStartX = e.clientX;
        dragStartY = e.clientY;

        const rect = scrollButton.getBoundingClientRect();
        buttonStartX = rect.left;
        buttonStartY = rect.top;

        scrollButton.style.transition = 'none';
        scrollButton.style.cursor = 'grabbing';

        // 添加全局事件监听
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);

        console.log('开始拖拽');
    }

    // 处理鼠标移动
    function handleMouseMove(e) {
        if (!isDragging) return;

        e.preventDefault();

        const deltaX = e.clientX - dragStartX;
        const deltaY = e.clientY - dragStartY;

        // 如果移动距离超过阈值，标记为已移动
        if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
            hasMoved = true;
        }

        const newX = buttonStartX + deltaX;
        const newY = buttonStartY + deltaY;

        // 限制在窗口范围内
        const maxX = window.innerWidth - scrollButton.offsetWidth;
        const maxY = window.innerHeight - scrollButton.offsetHeight;

        const constrainedX = Math.max(0, Math.min(newX, maxX));
        const constrainedY = Math.max(0, Math.min(newY, maxY));

        // 使用 left 和 top 定位
        scrollButton.style.left = constrainedX + 'px';
        scrollButton.style.top = constrainedY + 'px';
        scrollButton.style.right = 'auto';
        scrollButton.style.bottom = 'auto';
    }

    // 处理鼠标释放
    function handleMouseUp(e) {
        if (!isDragging) return;

        isDragging = false;
        scrollButton.style.transition = 'all 0.3s ease';
        scrollButton.style.cursor = 'pointer';

        // 移除全局事件监听
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);

        // 保存新位置
        saveButtonPosition();

        // 如果没有移动且点击时间很短，视为点击事件
        const clickDuration = Date.now() - clickStartTime;
        if (!hasMoved && clickDuration < 300) {
            setTimeout(() => handleButtonClick(), 50);
        }

        console.log('拖拽结束，移动距离:', hasMoved);
    }

    // 处理触摸开始（移动端）
    function handleTouchStart(e) {
        if (e.touches.length !== 1) return;

        e.preventDefault();

        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousedown', {
            clientX: touch.clientX,
            clientY: touch.clientY,
            button: 0
        });

        handleMouseDown(mouseEvent);

        // 添加触摸事件监听
        document.addEventListener('touchmove', handleTouchMove, { passive: false });
        document.addEventListener('touchend', handleTouchEnd);
    }

    // 处理触摸移动
    function handleTouchMove(e) {
        if (!isDragging || e.touches.length !== 1) return;

        e.preventDefault();

        const touch = e.touches[0];
        const mouseEvent = new MouseEvent('mousemove', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });

        handleMouseMove(mouseEvent);
    }

    // 处理触摸结束
    function handleTouchEnd(e) {
        e.preventDefault();

        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);

        const mouseEvent = new MouseEvent('mouseup', {
            button: 0
        });

        handleMouseUp(mouseEvent);
    }

    // 处理右键点击
    function handleRightClick(e) {
        e.preventDefault();
        scrollMethodIndex = (scrollMethodIndex + 1) % scrollMethods.length;
        scrollButton.title = `滚动方法 ${scrollMethodIndex + 1}/${scrollMethods.length} (可拖拽)`;
        console.log(`切换到滚动方法 ${scrollMethodIndex + 1}`);
    }

    // 处理按钮点击
    function handleButtonClick() {
        console.log('按钮被点击，当前滚动状态:', isScrolling);

        if (isScrolling) {
            stopAutoScroll();
        } else {
            startAutoScroll();
        }
    }

    // 开始自动滚动
    function startAutoScroll() {
        console.log('开始自动滚动，使用方法', scrollMethodIndex + 1);

        isScrolling = true;
        scrollButton.classList.add('scrolling');
        scrollButton.innerHTML = '⏸';
        scrollButton.title = '点击停止滚动';
        scrollAttempts = 0;

        // 立即滚动一次
        performScroll();

        // 然后开始定期检查和滚动
        scrollInterval = setInterval(() => {
            if (!isScrolling) return;
            performScroll();
        }, 2000); // 每2秒检查一次
    }

    // 停止自动滚动
    function stopAutoScroll() {
        console.log('停止自动滚动');

        isScrolling = false;
        scrollButton.classList.remove('scrolling');
        scrollButton.innerHTML = '↓';
        scrollButton.title = `滚动方法 ${scrollMethodIndex + 1}/${scrollMethods.length} (右键切换)`;

        if (scrollInterval) {
            clearInterval(scrollInterval);
            scrollInterval = null;
        }
        scrollAttempts = 0;
    }

    // 执行滚动操作
    function performScroll() {
        const currentScrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;

        console.log(`滚动尝试 ${scrollAttempts + 1}: 页面高度=${currentScrollHeight}, 当前位置=${currentScrollTop}`);

        // 检查是否有新内容加载
        if (currentScrollHeight !== lastScrollHeight) {
            console.log('检测到页面高度变化，从', lastScrollHeight, '到', currentScrollHeight);
            lastScrollHeight = currentScrollHeight;
            scrollAttempts = 0; // 重置尝试次数
        }

        // 使用当前选择的滚动方法
        try {
            scrollMethods[scrollMethodIndex](currentScrollHeight);
        } catch (error) {
            console.error('滚动方法执行失败:', error);
            // 如果当前方法失败，尝试下一个方法
            scrollMethodIndex = (scrollMethodIndex + 1) % scrollMethods.length;
            console.log('自动切换到方法', scrollMethodIndex + 1);
        }

        scrollAttempts++;

        // 如果尝试多次仍无效果，自动切换方法
        if (scrollAttempts >= 3) {
            const newScrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;
            if (Math.abs(newScrollTop - currentScrollTop) < 50) {
                console.log('当前方法无效，自动切换到下一个方法');
                scrollMethodIndex = (scrollMethodIndex + 1) % scrollMethods.length;
                scrollAttempts = 0;
            }
        }

        // 额外的兜底方法：尝试所有可能的滚动方式
        setTimeout(() => {
            if (isScrolling) {
                // 强制滚动方法
                window.scrollTo(0, document.body.scrollHeight);
                window.scrollTo(0, document.documentElement.scrollHeight);

                // 尝试滚动所有可滚动元素
                const scrollableElements = findScrollableElements();
                scrollableElements.forEach(element => {
                    try {
                        element.scrollTop = element.scrollHeight;
                    } catch (e) {
                        // 忽略错误
                    }
                });
            }
        }, 1000);
    }

    // 页面加载完成后初始化
    function init() {
        console.log('初始化多功能滚动插件');

        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', createScrollButton);
        } else {
            setTimeout(createScrollButton, 1000); // 延迟1秒确保页面完全加载
        }

        // 监听页面变化（处理单页应用）
        const observer = new MutationObserver((mutations) => {
            if (!scrollButton && document.body) {
                createScrollButton();
            }
        });

        if (document.body) {
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        } else {
            // 如果body还没加载，等待加载
            setTimeout(init, 500);
        }
    }

    // 清理函数
    function cleanup() {
        if (scrollButton) {
            scrollButton.remove();
            scrollButton = null;
        }
        if (scrollInterval) {
            clearInterval(scrollInterval);
            scrollInterval = null;
        }
        isScrolling = false;
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);

    // 初始化插件
    init();

})();
